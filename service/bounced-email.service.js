import AWS from 'aws-sdk';
import moment from 'moment-timezone';
import logger from '../utils/logger.js';

class BouncedEmailService {
  constructor() {
    this.documentClient = new AWS.DynamoDB.DocumentClient({
      region: process.env.AWS_REGION || 'us-west-2',
    });
  }

  /**
   * Check if email bounced 3+ times in last 7 days
   * @param {string} emailId - The email address to check
   * @returns {boolean} - True if email should be filtered
   */
  async isBouncedEmail(emailId) {
    try {
      const currentTime = moment.tz('UTC');
      const currentTimestamp = currentTime.format('YYYY-MM-DDTHH:mm:SS');
      const startTimestamp = currentTime.clone().subtract(7, 'days').format('YYYY-MM-DDTHH:mm:SS');

      const params = {
        TableName: 'bouncedemails',
        KeyConditionExpression: 'emailId = :emailId AND #timestamp BETWEEN :startTime AND :endTime',
        ExpressionAttributeNames: {
          '#timestamp': 'timestamp'
        },
        ExpressionAttributeValues: {
          ':emailId': emailId,
          ':startTime': startTimestamp,
          ':endTime': currentTimestamp
        }
      };

      const result = await this.documentClient.query(params).promise();
      return result.Items.length >= 3;
    } catch (error) {
      logger.error(`Error checking bounced email: ${emailId}`, { error: error.message });
      return false;
    }
  }

  /**
   * Add a bounced email record to DynamoDB
   * @param {string} emailId - The email address that bounced
   * @param {string} subject - The subject of the bounced email
   * @param {Array} to - Array of recipients
   * @returns {boolean} - True if successfully added
   */
  async addBouncedEmail(emailId, subject = '', to = []) {
    try {
      const timestamp = moment.tz('UTC').format('YYYY-MM-DDTHH:mm:SS');

      const params = {
        TableName: 'bouncedemails',
        Item: {
          emailId,
          timestamp,
          subject,
          to
        }
      };

      await this.documentClient.put(params).promise();
      logger.info(`Added bounced email record for: ${emailId}`);
      return true;
    } catch (error) {
      logger.error(`Error adding bounced email record: ${emailId}`, { error: error.message });
      return false;
    }
  }

  /**
   * Filter recipients to remove bounced emails
   * @param {Array} recipients - Array of recipient objects with email property
   * @returns {Array} - Filtered array of valid recipients
   */
  async filterValidRecipients(recipients) {
    if (!Array.isArray(recipients)) {
      return recipients;
    }

    const validRecipients = [];

    for (const recipient of recipients) {
      if (!recipient?.email) {
        continue;
      }

      const isBounced = await this.isBouncedEmail(recipient.email);
      if (!isBounced) {
        validRecipients.push(recipient);
      }
    }

    return validRecipients;
  }

  /**
   * No cleanup needed since DynamoDB handles persistence
   */
  destroy() {
    // Nothing to cleanup
  }
}

// Export singleton instance
export default new BouncedEmailService();
