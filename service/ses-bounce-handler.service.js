import logger from '../utils/logger.js';
import bouncedEmailService from './bounced-email.service.js';

/**
 * Service to handle SES bounce and complaint notifications
 * This service processes SNS notifications from AWS SES
 */
class SESBounceHandlerService {
  /**
   * Process SNS notification from SES
   * @param {Object} snsMessage - The SNS message from SES
   * @returns {boolean} - True if processed successfully
   */
  async processSESNotification(snsMessage) {
    try {
      const message = JSON.parse(snsMessage.Message);
      
      if (message.notificationType === 'Bounce') {
        return await this.handleBounce(message);
      } else if (message.notificationType === 'Complaint') {
        return await this.handleComplaint(message);
      }
      
      logger.warn('Unknown SES notification type', { notificationType: message.notificationType });
      return false;
    } catch (error) {
      logger.error('Error processing SES notification', { error: error.message, snsMessage });
      return false;
    }
  }

  /**
   * Handle bounce notifications from SES
   * @param {Object} bounceMessage - The bounce message from SES
   * @returns {boolean} - True if processed successfully
   */
  async handleBounce(bounceMessage) {
    try {
      const { bounce, mail } = bounceMessage;
      
      // Only process hard bounces (permanent failures)
      if (bounce.bounceType !== 'Permanent') {
        logger.info('Ignoring non-permanent bounce', { bounceType: bounce.bounceType });
        return true;
      }

      const subject = mail.commonHeaders?.subject || '';
      const recipients = bounce.bouncedRecipients || [];

      // Record each bounced email
      for (const recipient of recipients) {
        const emailAddress = recipient.emailAddress;
        if (emailAddress) {
          await bouncedEmailService.addBouncedEmail(
            emailAddress,
            subject,
            [emailAddress]
          );
          logger.info(`Recorded hard bounce for: ${emailAddress}`, {
            bounceSubType: recipient.diagnosticCode,
            timestamp: bounce.timestamp
          });
        }
      }

      return true;
    } catch (error) {
      logger.error('Error handling bounce notification', { error: error.message, bounceMessage });
      return false;
    }
  }

  /**
   * Handle complaint notifications from SES
   * @param {Object} complaintMessage - The complaint message from SES
   * @returns {boolean} - True if processed successfully
   */
  async handleComplaint(complaintMessage) {
    try {
      const { complaint, mail } = complaintMessage;
      const subject = mail.commonHeaders?.subject || '';
      const recipients = complaint.complainedRecipients || [];

      // Treat complaints as bounces for filtering purposes
      for (const recipient of recipients) {
        const emailAddress = recipient.emailAddress;
        if (emailAddress) {
          await bouncedEmailService.addBouncedEmail(
            emailAddress,
            subject,
            [emailAddress]
          );
          logger.info(`Recorded complaint as bounce for: ${emailAddress}`, {
            complaintFeedbackType: complaint.complaintFeedbackType,
            timestamp: complaint.timestamp
          });
        }
      }

      return true;
    } catch (error) {
      logger.error('Error handling complaint notification', { error: error.message, complaintMessage });
      return false;
    }
  }

  /**
   * Validate SNS message signature (basic validation)
   * @param {Object} snsMessage - The SNS message
   * @returns {boolean} - True if valid
   */
  validateSNSMessage(snsMessage) {
    // Basic validation - in production, you should verify the SNS signature
    return snsMessage && 
           snsMessage.Type && 
           snsMessage.Message && 
           (snsMessage.Type === 'Notification' || snsMessage.Type === 'SubscriptionConfirmation');
  }
}

// Export singleton instance
export default new SESBounceHandlerService();
