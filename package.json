{"name": "smart-alert-email-notification-service", "version": "1.0.0", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"@sentry/node": "^7.0.0", "aws-sdk": "^2.1520.0", "dotenv": "^16.3.1", "joi": "^17.11.0", "kafkajs": "^2.2.4", "kafkajs-snappy": "^1.1.0", "moment-timezone": "^0.5.43", "nodemailer": "^6.9.4", "snappy": "^7.2.2", "uuid": "^9.0.0", "winston": "^3.9.0", "winston-transport-sentry-node": "^2.7.0"}, "devDependencies": {"nodemon": "^2.0.22"}}