import 'dotenv/config';
import * as Sentry from '@sentry/node';
import kafkaConnection from './connection/kafka.js';
import { isValidMessagePacket } from './helper/index.js';
import NotificationFacade from './service/NotificationFacade.js';
import kafkaConfig from './config/kafka.js';
import logger from './utils/logger.js';

async function onMessageHandler(message) {
  try {
    if (!isValidMessagePacket(message)) {
      return;
    }

    const notificationFacade = new NotificationFacade();
    await notificationFacade.sendNotification(message);
  } catch (error) {
    error.message = `Message processing error: ${error.message}`;
    throw error;
  }
}

const initializeService = async () => {
  try {
    await kafkaConnection.connect();
    await kafkaConnection.subscribe(kafkaConfig.topics.emailNotification, onMessageHandler);
    logger.info('🧩 Email notification service started successfully');
  } catch (error) {
    logger.error(`Failed to initialize email notification service`, { error });
    await Sentry.flush();
    process.exit(1);
  }
};

const gracefulShutdown = async () => {
  try {
    await Sentry.flush();
    await kafkaConnection.disconnect();
    logger.info('Email notification service shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error(`Error during email notification service shutdown`, { error });
    process.exit(1);
  }
};

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

initializeService();
